// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 2025/7/31 10:58:32
const configMerger = require("D:/demo/ai-chat/node_modules/.pnpm/@nuxtjs+tailwindcss@6.12.2__f9d8d3091335ea7fd0f78c2f18f92d7a/node_modules/@nuxtjs/tailwindcss/dist/runtime/merger.js");

const inlineConfig = {"content":[],"theme":{"extend":{}},"plugins":[]};

const config = [

].reduce((prev, curr) => configMerger(curr, prev), configMerger(inlineConfig, { content: { files: ["D:/demo/ai-chat/examples/nuxt-openai/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/demo/ai-chat/examples/nuxt-openai/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/demo/ai-chat/examples/nuxt-openai/components/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/demo/ai-chat/examples/nuxt-openai/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/demo/ai-chat/examples/nuxt-openai/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","D:/demo/ai-chat/examples/nuxt-openai/plugins/**/*.{js,ts,mjs}","D:/demo/ai-chat/examples/nuxt-openai/composables/**/*.{js,ts,mjs}","D:/demo/ai-chat/examples/nuxt-openai/utils/**/*.{js,ts,mjs}","D:/demo/ai-chat/examples/nuxt-openai/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","D:/demo/ai-chat/examples/nuxt-openai/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","D:/demo/ai-chat/examples/nuxt-openai/app.config.{js,ts,mjs}"] } }));

module.exports = config

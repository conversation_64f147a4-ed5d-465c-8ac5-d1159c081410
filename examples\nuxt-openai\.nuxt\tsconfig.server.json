{"compilerOptions": {"forceConsistentCasingInFileNames": true, "strict": true, "noEmit": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowJs": true, "resolveJsonModule": true, "jsx": "preserve", "allowSyntheticDefaultImports": true, "jsxFactory": "h", "jsxFragmentFactory": "Fragment", "paths": {"#imports": ["./types/nitro-imports"], "~/*": ["../*", "../*"], "@/*": ["../*", "../*"], "~~/*": ["../*", "../*"], "@@/*": ["../*", "../*"], "nitropack/types": ["../../../node_modules/.pnpm/nitropack@2.10.4_@upstash+r_9a1e3a61bba08bfa8a019cfa07179d8d/node_modules/nitropack/types"], "nitropack": ["../../../node_modules/.pnpm/nitropack@2.10.4_@upstash+r_9a1e3a61bba08bfa8a019cfa07179d8d/node_modules/nitropack"], "defu": ["../../../node_modules/.pnpm/defu@6.1.4/node_modules/defu"], "h3": ["../../../node_modules/.pnpm/h3@1.13.0/node_modules/h3"], "consola": ["../../../node_modules/.pnpm/consola@3.2.3/node_modules/consola"], "ofetch": ["../../../node_modules/.pnpm/ofetch@1.4.1/node_modules/ofetch"], "@unhead/vue": ["../../../node_modules/.pnpm/@unhead+vue@1.11.11_vue@3.5.13_typescript@5.8.3_/node_modules/@unhead/vue"], "@vue/compiler-sfc": ["../../../node_modules/.pnpm/@vue+compiler-sfc@3.5.13/node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../../../node_modules/.pnpm/unplugin-vue-router@0.10.8__69f06aac1278db471ec18b73aeeeb739/node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../../../node_modules/.pnpm/@nuxt+schema@3.14.159_magicast@0.3.5_rollup@4.34.9/node_modules/@nuxt/schema"], "nuxt": ["../../../node_modules/.pnpm/nuxt@3.14.159_@parcel+watch_6cbafd38c5bf621a3b8172cab1bafee0/node_modules/nuxt"], "~": ["./.."], "@": ["./.."], "~~": ["./.."], "@@": ["./.."], "#shared": ["../shared"], "assets": ["../assets"], "public": ["../public"], "#build": ["./"], "#build/*": ["./*"], "#internal/nuxt/paths": ["../../../node_modules/.pnpm/nuxt@3.14.159_@parcel+watch_6cbafd38c5bf621a3b8172cab1bafee0/node_modules/nuxt/dist/core/runtime/nitro/paths"], "#vue-router": ["./vue-router"]}, "lib": ["esnext", "webworker", "dom.iterable"]}, "include": ["./types/nitro-nuxt.d.ts", "../../../node_modules/.pnpm/@nuxtjs+tailwindcss@6.12.2__f9d8d3091335ea7fd0f78c2f18f92d7a/node_modules/@nuxtjs/tailwindcss/runtime/server", "../../../node_modules/.pnpm/@nuxt+devtools@1.6.3_rollup_8dd8d626746a8e4cb864a0db196f36e7/node_modules/@nuxt/devtools/runtime/server", "./types/nitro.d.ts", "../**/*", "../server/**/*"], "exclude": ["../node_modules", "../../../node_modules", "../../../node_modules/.pnpm/nuxt@3.14.159_@parcel+watch_6cbafd38c5bf621a3b8172cab1bafee0/node_modules/nuxt/node_modules", "../dist"]}
// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/chat-with-vision': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/chat-with-vision').default>>>>
    }
    '/api/chat': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/chat').default>>>>
    }
    '/api/completion': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/completion').default>>>>
    }
    '/api/use-chat-request': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/use-chat-request').default>>>>
    }
    '/api/use-chat-tools': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../server/api/use-chat-tools').default>>>>
    }
    '/__nuxt_error': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../../../node_modules/.pnpm/nuxt@3.14.159_@parcel+watch_6cbafd38c5bf621a3b8172cab1bafee0/node_modules/nuxt/dist/core/runtime/nitro/renderer').default>>>>
    }
  }
}
export {}
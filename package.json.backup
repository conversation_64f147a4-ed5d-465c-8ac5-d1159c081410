{"private": true, "name": "ai-repo", "scripts": {"build": "turbo build --concurrency 16", "build:examples": "turbo build --filter=@example/*", "build:packages": "turbo build --filter=@ai-sdk/* --filter=ai", "changeset": "changeset", "clean": "turbo clean", "dev": "turbo dev --no-cache --concurrency 19 --continue", "lint": "turbo lint", "prepare": "husky install", "update-references": "update-ts-references", "prettier-check": "prettier --check \"**/*.{js,ts,tsx,md,mdx,svelte}\"", "type-check": "tsc --build", "type-check:full": "tsc --build tsconfig.with-examples.json", "prettier-fix": "prettier --write \"**/*.{js,ts,tsx,md,mdx,svelte}\"", "publint": "turbo publint", "test": "turbo test --concurrency 16 --filter=!@example/*", "test:update": "turbo test:update --concurrency 16 --filter=!@example/*", "ci:release": "turbo clean && turbo build && changeset publish", "ci:version": "changeset version && node .github/scripts/cleanup-examples-changesets.mjs && pnpm install --no-frozen-lockfile", "clean-examples": "node .github/scripts/cleanup-examples-changesets.mjs && pnpm install --no-frozen-lockfile"}, "lint-staged": {"*": ["prettier --ignore-unknown --write"]}, "devDependencies": {"update-ts-references": "^3.6.0", "@changesets/cli": "2.27.10", "@playwright/test": "^1.44.1", "eslint": "8.57.1", "eslint-config-vercel-ai": "workspace:*", "husky": "^8.0.0", "lint-staged": "15.2.10", "next": "15.0.0-canary.23", "playwright": "^1.44.1", "prettier": "3.5.3", "prettier-plugin-svelte": "^3.3.3", "publint": "0.2.12", "react": "19.0.0-rc-cc1ec60d0d-20240607", "react-dom": "19.0.0-rc-cc1ec60d0d-20240607", "turbo": "2.4.4", "typescript": "5.8.3", "vitest": "2.1.4"}, "engines": {"node": "^18.0.0 || ^20.0.0 || ^22.0.0"}, "homepage": "https://ai-sdk.dev/docs", "repository": {"type": "git", "url": "git+https://github.com/vercel/ai.git"}, "license": "Apache License", "bugs": {"url": "https://github.com/vercel/ai/issues"}, "keywords": ["ai"], "packageManager": "pnpm@10.11.0", "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "arrowParens": "avoid", "trailingComma": "all", "plugins": ["prettier-plugin-svelte"], "overrides": [{"files": "*.svelte", "options": {"parser": "svelte"}}]}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}
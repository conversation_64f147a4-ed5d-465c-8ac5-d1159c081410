{"$schema": "https://turbo.build/schema.json", "globalEnv": ["CI", "PORT"], "tasks": {"build": {"dependsOn": ["^build"], "env": ["CI", "PORT", "NODE_ENV", "NEXT_RUNTIME", "OPENAI_API_KEY", "OPENAI_API_BASE", "ANTHROPIC_API_KEY", "DEEPSEEK_API_KEY", "VERCEL_API_KEY", "VERCEL_DEPLOYMENT_ID", "VERCEL_ENV", "VERCEL_OIDC_TOKEN", "VERCEL_REGION", "VERCEL_URL"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", ".nuxt/**", ".svelte-kit/**", ".vinxi/**"]}, "lint": {"dependsOn": ["^lint"]}, "type-check": {"dependsOn": ["^build", "build"]}, "test": {"dependsOn": ["^build", "build"]}, "test:update": {"dependsOn": ["^build", "build"]}, "publint": {"dependsOn": ["^build", "build"]}, "clean": {"dependsOn": ["^clean"]}, "dev": {"cache": false, "persistent": true}, "prettier-check": {}, "integration-test": {"dependsOn": ["^build", "build"]}}}